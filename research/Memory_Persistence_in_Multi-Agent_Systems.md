# Memory Persistence in Multi‑Agent Systems

## Overview of State Persistence Needs

Multi-agent systems require robust **state persistence** to function over extended sessions and across
restarts. Agents collaborating on complex tasks – like a software development agent planning, coding, and
deploying – must remember past actions, shared knowledge, and context beyond a single prompt window.
This memory spans **short-term context** (recent messages, intermediate results) and **long-term
knowledge** (learned facts, user preferences, or domain data). Effective memory systems enable session
recovery after crashes, distributed agents sharing state, time-based forgetting, and resilience against data
loss.

Modern AI agent frameworks use diverse approaches for persistence. Mainstream methods include
**relational databases** (storing structured state and logs), **key–value caches** (fast retrieval of recent
context), **vector stores** (semantic embedding-based recall for Retrieval-Augmented Generation, _RAG_ ),
**graph databases** (knowledge graphs encoding relationships and history), and lightweight **embedded
stores** (local file or database engines). Each approach comes with trade-offs in terms of speed, scalability,
query capability, and complexity. Below, we compare these storage options and examine how leading
frameworks like AutoGen, CrewAI, and LangGraph implement memory. We also contrast RAG-style
“memories” versus traditional databases, and detail how frameworks handle session recovery, shared
memory, TTL/expiry policies, and crash resilience.

## Approaches to Memory Storage

### Relational Databases (SQL-Based)

**Relational databases** provide structured, durable storage for agent state. They store data in tables with
predefined schemas, enabling complex queries (SQL) and strong consistency (ACID transactions). In multi-
agent settings, relational DBs often log conversations, tool results, or agent actions in structured form. For
example, the CrewAI framework uses **SQLite3** for long-term memory persistence. Each agent’s
output or “task result” can be stored as a row in a table, preserving knowledge across sessions. A simple
schema might be a messages table with columns: session_id, role (user/agent), content,
timestamp – allowing retrieval of entire conversation history by session. Relational stores shine for
**precise retrieval** (e.g. fetch the last user query, or all code-generation outputs) and for structured data (like
a table of discovered facts or tasks).

**Trade-offs:** Relational DBs excel in reliability and exact queries, but are less suited for semantic searches on
unstructured text. If an agent asks “what was the user’s request last week?”, a SQL database can retrieve the
exact entry by ID or date – high precision and no vector math needed. In fact, some argue a robust SQL
database plus a text-to-SQL agent query can outperform a pure vector search for factual recall. The
downside is the **rigidity** of schema and the overhead of managing a heavy database service if using
something like Postgres. For lightweight needs, embedded relational engines like SQLite are convenient (no
separate server). However, at large scale or high throughput, a server-based DB (Postgres/MySQL) might be

```
1 2
```
```
3
```

required for better concurrency and volume handling. CrewAI’s reliance on SQLite is simple and file-based,
but could **limit scalability** under heavy loads. If multiple agents/processes need concurrent access, a
central SQL server (or a proper file locking strategy for SQLite) is necessary.

**Use cases:** Relational DBs are ideal for **structured long-term state** : e.g. storing a registry of all completed
tasks, user profiles/preferences, or an index of code snippets with keys. They also facilitate analytics on
agent performance (since you can run SQL queries aggregating outcomes). Frameworks that value
reliability often integrate SQL – CrewAI’s long-term memory DB (long_term_memory_storage.db)
persists results across runs. For an autonomous coding agent, one might use Postgres (with the
PGVector extension) to store both code embeddings and associated metadata in a single durable store,
blending structured and vector queries.

### Key–Value Caches (In-Memory or Distributed KV Stores)

**Key–value stores** provide fast lookup by key, making them useful for caching state or sharing memory in
distributed setups. A classic example is **Redis** , an in-memory data store that can persist to disk and
supports TTL (time-to-live) expiry on keys. In multi-agent systems, a KV store can hold the latest
conversation state or intermediate results under a known key (like "session:ABC:latest_plan").
Because Redis is networked, multiple agent processes (or microservices) can read/write the same keys,
enabling **shared memory** across a distributed team of agents. For instance, LangGraph’s recent Redis
integration uses RedisStore for cross-thread memory, and RedisSaver for per-thread checkpoints

. This allows an agent to maintain conversation continuity in one thread and also share long-term
facts via a Redis namespace, all with sub-millisecond latency.

Key–value caches are simple: they treat memory as a **dictionary**. You can store JSON-serialized state or even
entire conversation histories under a key. Retrieval is fast by exact key or by simple patterns (Redis supports
scanning keys or basic secondary structures like sorted sets). They naturally support **TTL/expiry** , which is
useful for ephemeral short-term memory – e.g. auto-expire a conversation context after 24 hours for
privacy, or remove stale data to free memory. A developer can set an expiry when writing to Redis (e.g.
SET key value EX 3600). This built-in TTL mechanism is a convenient way to implement “forgetting” of
old state.

**Trade-offs:** Key–value stores are extremely fast and scalable (Redis clusters can handle high throughput).
However, they lack built-in semantic search or complex query capability – you usually need the exact key or
to iterate. They are best for **small, frequently accessed pieces of state** (like “last user message” or “agent’s
working memory scratchpad”). Also, if not carefully configured to persist (e.g. Redis with RDB or AOF), pure
in-memory caches can lose data on crash. In practice, enabling disk persistence or using Redis’s replication
can give crash resilience. Another limitation is data modeling: storing large knowledge in a flat KV can
become unwieldy (you can end up reinventing indexing).

**Use cases:** Use KV caches for **real-time collaborative memory** and small shared contexts. For example,
multiple agents in a tool automation pipeline might publish their partial results to a Redis channel or list so
others can consume them in real-time (a blackboard pattern). LangGraph’s memory store concept (see
below) by default is an in-memory Python dict for simplicity, but it can be swapped out with Redis for
durability. If building a Rust-based agent, one could use the redis crate to store conversation
state: e.g. HSET "session:123" history "<JSON>" to cache the dialogue, retrieving it in the next run
to restore context.

```
4
```
```
1
```
```
5 6
7
```
```
8 6
```

### Vector Stores (Semantic Embedding Memory)

**Vector stores** have become a mainstream solution for LLM “memory”, enabling **Retrieval-Augmented
Generation (RAG)**. In this approach, chunks of text (documents, conversation snippets, facts) are
embedded into high-dimensional vectors, which are stored in a specialized database that supports
similarity search. When the agent needs to recall something relevant, it computes an embedding of the
query and finds the nearest vectors, retrieving the associated text. Many autonomous agent systems use
vector DBs to give the LLM semantic recall of past information that doesn’t fit in the prompt window. For
example, AutoGPT and BabyAGI were early adopters of Pinecone or FAISS for long-term memory, storing
notes and intermediate results as vectors. AutoGPT v0.6 even introduced a unified “DB manager” to
handle different vector backends.

Frameworks often integrate vector stores via plugins or libraries. **AutoGen** (Microsoft’s multi-agent
framework) provides a Memory interface that can plug into vector DBs like **ChromaDB** for persistent
semantic memory. In an example, AutoGen indexes documentation by chunking it and adding to a
ChromaDBVectorMemory store, persisted on disk (at ~/.chromadb_autogen). CrewAI likewise uses
**Chroma (an open-source vector DB)** under the hood for its short-term memory: by enabling memory,
CrewAI will spin up local Chroma DB files for current context memory. LangGraph’s design allows
semantic search on its memory store by configuring an embedding model for the store; e.g. you can attach
OpenAI’s text-embedding model to embed certain fields of your memories for semantic queries.
Even Redis has vector search support now – as seen in the LangGraph Redis integration, Redis can store
embeddings and perform similarity queries, combining short-term and long-term memory in one system
.

**Trade-offs:** Vector memory excels at **fuzzy recall** of unstructured data. It can retrieve a relevant past user
query or a document passage even if the wording differs, as long as the semantic meaning is similar. This
greatly enhances an agent’s ability to use past knowledge in generating responses. However, **RAG is not a
silver bullet for agent memory**. By focusing on similarity, it may retrieve semantically related snippets that
lack the exact fact needed, or it might miss relevant context if the query isn’t a close match in vector space

. Vector stores treat knowledge as isolated points in space with no explicit relationships between them
. They typically do not capture temporal ordering or causality – yesterday’s and today’s facts might be
retrieved with equal weight if similar. Another consideration is maintenance: vector DBs can grow large as
memories accumulate, so one must implement pruning or updating of outdated info (some vector DBs
support deleting or re-embedding records, but it’s a manual process to manage versioning of knowledge).

Performance-wise, specialized vector databases (like Pinecone, Weaviate, Qdrant, or Chroma) are optimized
for similarity search and can handle millions of vectors with indexes (HNSW, IVF etc.). Many can scale
horizontally. The complexity is moderate: you need an embedding model available and logic to convert
agent data to vectors and back. From Rust, one might use an API (HTTP) to a service like Qdrant or Vespa, or
use a local library – though pure Rust vector DB libraries are nascent, one could integrate tantivy (for
text+vector search) or call Python via FFI for something like FAISS if needed.

**Use cases:** Vector memory is best for **knowledge retrieval** – providing background facts, documentation,
or recalling user preferences by semantic similarity. For a coding agent, a vector store could index a library
of code snippets or previous bug-fix descriptions so the agent can retrieve relevant examples when writing
new code. Many frameworks implement **hybrid memory** : using vector search to fetch relevant long-term
info and then storing it as context for the agent’s prompt (this is a typical RAG cycle ). We will later

```
9
10
```
```
11 12
13
```
```
14 15
```
```
16 17
```
```
18 19
```
```
20
20
```
```
21 22
```

contrast this with more structured memory; in practice a combination is often ideal (e.g. vector search for
static knowledge, but also using keys/relations for dynamic state).

### Graph Databases (Knowledge Graphs)

**Graph databases** store information as nodes and edges, well-suited for representing relationships,
ontologies, and temporal evolutions in an agent’s memory. Instead of treating each memory item as
independent (like in a vector store), a knowledge graph can encode how facts relate: e.g. Agent A
contributed to Task X, which depends on Library Y; or user U likes product P until date D. This structured
representation is powerful for **reasoning and temporal context**. A graph DB (like Neo4j, TigerGraph, or
even an RDF store) allows queries like “find all past tasks related to database migrations that succeeded” or
“what does the user currently prefer, given their changing history?”. It’s a more **knowledge-centric
memory** , reminiscent of human semantic memory (facts and their connections).

In the context of LLM agents, graph-based memory is a lesser-known but emerging approach. The Zep
memory service, for example, advocates for **knowledge graphs as agent memory** as opposed to pure RAG

. Zep’s open-source framework **Graphiti** builds a real-time dynamic knowledge graph for agents
. It tracks not just facts but also the timing (temporal validity) of those facts, enabling the agent to
answer time-based queries like “What did the user prefer in February?”. Graphiti attaches timestamps
(valid_at, invalid_at, etc.) to each relationship, so the agent can retain a history of changes.
This approach also helps with **conflict resolution** : if new information conflicts with old (e.g. user’s
preference changed), the graph can mark the old edge as invalidated and link the new fact as the cause
. This is far more expressive than simply embedding two contradictory statements in a vector DB.

**Trade-offs:** Graph memory provides rich, structured context and supports complex queries (graph
traversals, pattern matching) that an LLM or a symbolic reasoner can leverage. It’s excellent for **dynamic,
relational data** – multi-agent systems where state involves many entities and relations (people, tools, tasks,
code modules, etc.) can benefit from a graph that the agent can query or even walk via a tool. The cost is
**complexity** : designing a schema (ontology) for the graph, updating it in real-time, and writing queries
(Cypher, Gremlin, etc.) or having the agent interpret graph data. Graph databases may also be slower for
large-scale pure lookup than a vector search (though indexes help). There’s also a question of integration:
an LLM might need a sub-module or tool to query the graph; you might implement a natural language ->
graph query translator or pre-defined query functions. In practice, some systems combine both: e.g.
Graphiti uses a **hybrid retrieval** approach, performing semantic vector search **and** graph traversal to get
the best of both. It can do a vector search to find a relevant node, then follow edges to pull related facts,
and even incorporate keyword search (BM25) on text fields – finally fusing results for the LLM. This
multi-step retrieval yields comprehensive context in milliseconds.

**Use cases:** Graph DBs are valuable when an agent needs to maintain a world model or **complex memory
of state**. For example, in a project management agent with multiple developers, a graph could link each
bug report to code modules, to the developer who fixed it, to the date, etc. An agent can then reason about
who to assign a new bug by traversing similar issue nodes. Or consider a **conversational AI** that keeps a
profile of the user: graph memory can represent the user’s preferences, past interactions, and changing
sentiment explicitly (as nodes like User->Preference->Item). This is what some commercial agent platforms
aim for – a “unified user graph” that accumulates all knowledge about a user. In multi-agent teams, a
graph could also represent the **coordination structure** : each agent node, their roles, and shared resources,
making it easier to share structured memory. While not as commonly used as vector stores, graph-based

```
23 24 25
26
27
28 29
```
```
30
31
```
```
32
32
33 34
```
```
35
```

memory is on the rise for production systems that need deeper reasoning and temporal awareness.
Open-source projects like **Mem0** and Graphiti are pushing this concept, showing that agent memory is
evolving beyond just document retrieval.

### Embedded and Local Stores

Embedded stores refer to lightweight, locally hosted databases or files used for persistence. These can be
relational (SQLite), key-value (like **Sled** or **RocksDB** in Rust), or even custom file formats (JSON logs, CSV,
etc.). The distinguishing factor is that they run in-process or on the local machine, without a separate server,
which simplifies deployment. We’ve already touched on SQLite (embedded SQL) as used by CrewAI. Other
examples include **ChromaDB’s default mode** , which is an embedded vector store persisting to a directory –
no server needed, just files on disk. Agents can also use simple file writes as memory: for instance, an
agent might append each conversation turn to a text file for later loading, or dump its planning state to a
JSON file at checkpoints. The **“ever-growing skill library”** in the Voyager Minecraft agent was essentially an
embedded memory: it wrote new code functions to its local filesystem (the agent’s codebase), which acted
as a persistent memory of learned skills. Similarly, a software development agent could persist
generated code to a repository (git) – effectively treating the repo as long-term memory of what has been
done so far.

**Trade-offs:** Embedded stores are typically easy to use and require minimal setup. They work well for **single-
machine or single-process** scenarios. Performance is often excellent for moderate data sizes since there’s
no network overhead (e.g. SQLite can handle thousands of writes per second on an SSD, and Sled or
RocksDB can handle key-value ops quickly on local storage). Crash resilience depends on the store – many
embedded DBs (SQLite, RocksDB) are durable and recover journals on crash, but a custom file write might
need careful flushing to disk to avoid losing data if the process dies. One limitation is that embedded stores
are not directly shareable across machines (you’d need a shared drive or to sync files). If you need multiple
agents on different hosts to share memory, a purely local store won’t suffice; you’d either promote it to a
centralized server or have an agent act as a server for others.

**Use cases:** These stores are great for **prototyping and small-scale agents** , or as a **local cache** layer in
front of a more persistent backend. For example, an agent could keep a local RocksDB instance caching the
last 1000 interactions for quick access, while also pushing important facts to a cloud database for longevity.
CrewAI by default uses local files (under an app data folder) to save short-term and vector data – this
makes it simple for users to get started without configuring any external DB. A Rust-based agent might use
sled (a pure-Rust embedded KV store) to persist key info between runs: e.g. using sled to map
conversation IDs to a serialized struct of conversation state. That gives the speed of an in-process DB with
the safety of on-disk persistence. One must ensure to properly close or sync such DBs on shutdown to avoid
corruption.

**Comparison of Storage Options:** The following table summarizes trade-offs of these approaches:

```
23
```
```
23 36
```
```
15
```
```
37
```
```
15
```

Approach Persistence &
Scope
Query Capabilities Performance &
Scalability
Example Use Cases

**Relational
DB**

```
Durable (ACID).
Local (SQLite) or
network
(Postgres).
Suitable for
cross-session
data.
```
```
Precise SQL
queries, filtering,
aggregations.
(Extensions for
vectors available)
```
```
Moderate speed;
scales with
indexing and
vertical scaling (or
clustering for
some DBs).
```
```
Structured logs, user
profiles, chat history.
E.g. CrewAI uses
SQLite for long-term
memory.
```
**Key–Value
Store**

```
In-memory with
optional disk
persist (Redis).
Networked for
multi-process
sharing.
```
```
Fast key lookup;
simple data
structures (hash,
list). No inherent
semantic search.
```
```
Very high
throughput (sub-
ms). Scales
horizontally
(sharding) and via
replication.
```
```
Ephemeral context,
distributed caches,
fast-sharing state. E.g.
LangGraph + Redis for
conversation threads
.
```
**Vector
Store**

```
Persistent (if
configured). Can
be local (Chroma,
FAISS) or cloud
(Pinecone). Often
per-project scope
or global
knowledge base.
```
```
k-NN similarity
search on
embeddings;
metadata filters.
Great for
semantic queries,
not for structured
logic.
```
```
Optimized for
high-dim similarity;
single-vector query
in milliseconds.
Many scale-out
options (shards,
HNSW indexes).
```
```
Long-term semantic
memory, knowledge
base docs, past
dialogs. E.g. AutoGen
with Chroma for doc
Q&A ;
AutoGPT/Pinecone for
agent memory.
```
**Graph DB**

```
Durable. Usually
server-based
(Neo4j) but can
embed (e.g. RDF
stores). Scope
can be global
knowledge graph
or user-specific
subgraph.
```
```
Rich relationship
queries (graph
traversals,
pattern match).
Can do graph
algorithms,
temporal queries.
```
```
Depends on data
size and query;
traversals can be
slower than key
lookups, but
indexes help.
Scales via
clustering or
specialized
engines.
```
```
Dynamic context with
entities & relations,
reasoning about
history. E.g. Zep’s
Graphiti for user
memory with
temporal edges
```
. Best for complex
domains needing
structured knowledge.

```
2
```
```
5 6
```
```
11 12
```
```
10
```
```
28
29
```

```
Approach Persistence &
Scope
Query Capabilities Performance &
Scalability
Example Use Cases
```
```
Embedded
Store
```
```
Durable (if file-
backed) on local
disk. Scope is
typically per-
agent instance.
No external
service needed.
```
```
Matches the type
(SQL for SQLite,
key lookup for
Sled, etc.). Usually
no remote query
access (local
only).
```
```
Very fast in-
process; limited by
single machine
resources. Not
easily scalable
beyond one
process (though
multiple can share
SQLite file in read
mode).
```
```
Quick persistence for
single-agent apps,
local caching. E.g.
CrewAI default
memory files ;
using Sled in a Rust
agent to persist
settings. Often used in
dev or small
deployments, or as
local cache for a cloud
DB.
```
## RAG-Style Memory vs. Traditional Databases

A key debate in agent design is using **RAG-style memory** versus **traditional databases** for knowledge
recall. Retrieval-Augmented Generation (RAG) treats memory as an **information retrieval** problem: the
agent’s context is augmented with documents or snippets retrieved via semantic similarity. In
contrast, using a database is treating memory as **factual storage** : the agent explicitly queries for facts (by
keys or structured queries) and then uses them. Both retrieve information to support the LLM, but they
serve different needs.

**RAG-Style Memory (Vector stores):** This shines when dealing with **unstructured text knowledge**. For
example, a software agent might ingest API documentation or past project notes into a vector index. When
a question arises (“How do I connect to the database in this framework?”), the agent uses embeddings to
pull the most relevant doc snippets and includes them in the prompt. This approach improves relevance,
accuracy, and richness of responses by grounding them in retrieved context. Many current agents use
RAG as a form of long-term memory: retrieving old messages or facts that are semantically similar to the
current query. It’s model-agnostic: the LLM doesn’t “remember” anything permanently, but the system
injects memory by retrieving text.

However, **RAG is not sufficient as a full memory system** in many cases. It lacks continuity and
structured understanding. For instance, if an agent’s goal is to track a user’s changing preferences over
time, purely embedding each interaction and searching by similarity might fail to capture the chronological
sequence or causality. As one commentary put it, relying solely on vector search can be a “circus” of hits and
misses for agent memory. You might get semantically related bits, but miss the factual chain (the _why_
and _when_ something changed). RAG also typically doesn’t automatically expire outdated info – if a fact is
updated, the old embedding might still surface unless you purge it. This is why frameworks like **Graphiti**
emphasize that _agent memory != knowledge retrieval_. In their view, an agent’s memory should capture
dynamic state, not just retrieve static documents.

**Traditional Databases:** Using a SQL or key-value database for memory emphasizes **precision and
reliability**. If you know what you’re looking for (exact keys or structured criteria), a database query returns
the exact fact. For example, an agent could use a SQL query to find “the last time error X occurred and how

```
15
```
```
21 22
```
```
38
```
```
39
```
```
36
```
```
23 36
```
```
40
20
```
```
23
```

it was resolved” if such events are logged in a table – this is deterministic and won’t bring in tangentially
similar data. For applications where data can be structured (transactions, records, configurations), a
relational DB paired with a text-to-SQL capability can answer complex questions with 100% accurate data
retrieval. This approach avoids the vector search’s false positives and provides an **audit trail** (you can
trace how the agent got the data from the DB).

That said, traditional DB queries struggle with **semantic nuance**. If a user asks a vague question and the
relevant knowledge isn’t keyed in a straightforward way, a SQL query might not find it. Also, developing a
good schema in advance for all the knowledge an agent might need is challenging – unstructured
conversations or commonsense knowledge don’t fit neatly into tables. There’s a middle ground: some
teams store data in a DB and use an LLM to generate SQL queries on the fly (so the LLM can interpret a
natural language question into structured form). This can work, but it pushes complexity to prompt
engineering and requires the schema to be rich enough.

**Hybrid Approaches:** In practice, many systems combine both. One pattern is **store-then-index** : e.g. log all
interactions in a database for reliability, but also create an embedding index for semantic search over that
log. Another pattern is **graph-enhanced RAG** : e.g. Microsoft’s “GraphRAG” which combines a knowledge
graph with retrieval – though one analysis noted it was limited by relying on LLM summarization and was
slower. The Graphiti approach we discussed blends semantic search, keyword search, and graph
traversal , illustrating how a memory system can use multiple query modalities. The bottom line is to
**choose the right tool for the data** : use RAG for static knowledge bases or when you expect semantic
matches, and use structured storage for dynamic, critical state that needs exact tracking. For
example, an AI coding assistant might use RAG to pull in relevant documentation, but use a structured store
to track the state of the current coding project (files created, requirements met, etc.).

Finally, consider **maintenance and updates** : In a traditional DB, updating or invalidating a record is
straightforward (update or delete SQL). In a pure vector store, one must locate and delete the old vector,
and possibly re-embed updated text – extra steps often done in batch. Knowledge graphs explicitly handle
updates by adding/removing edges (Graphiti’s temporal tagging shows one way to mark facts as expired
). So if your agent’s knowledge changes frequently or has conflicting updates, a graph or relational store
can handle that logic more transparently than raw RAG.

**Conclusion:** RAG-style memory gives breadth and semantic agility – critical for understanding context in an
open-ended way – whereas traditional databases give exactness and structured depth. An autonomous
agent benefits from both: e.g., use a vector search to recall _which_ parts of documentation might be relevant
to a problem, then use a database to log _what steps it took and their outcomes_ so it can exactly retrieve
outcomes of similar past tasks. Many frameworks now support multi-modal memory to address this:
LangGraph explicitly allows both vector-based memory search and retrieving state from checkpoints;
CrewAI uses RAG for contextual recall but logs results in SQLite for persistent reference. As a
developer, you should tailor the memory layer to what your agent will do – if it’s planning and coding,
ensure it can recall past plans and code (which might be semi-structured), not just raw text.

```
3
```
```
41
32
```
```
41 42
```
```
28
```
```
1 2
```

## Session Recovery and Fault Tolerance

For long-running autonomous agents, the ability to **recover state after a crash or restart** is paramount.
Without it, an agent that crashes (or is shut down for maintenance) would wake up “amnesiac,” unable to
build on prior work. Several frameworks provide mechanisms for session recovery:

```
LangGraph (Checkpointing): LangGraph’s design centers on persistent state through checkpoints.
Every “super-step” of the agent’s graph execution can be automatically checkpointed to a persistence
layer. A unique thread ID identifies a sequence of runs (like a conversation or task thread), and
each checkpoint is a snapshot of the entire graph state at a given time. After a crash,
LangGraph can reload the latest checkpoint for that thread and restore the agent’s state (variables,
what node was next, etc.). This effectively gives fault tolerance and even time-travel debugging
(you can inspect or roll back to prior checkpoints). By default, LangGraph can use an in-memory
saver or simple file-based saver, but it also offers robust options like the Redis-based saver for
persistence in external storage. The developer doesn’t have to manually save state; the
framework handles it behind the scenes, ensuring that each step’s state is saved in the thread’s log
```
. This approach is reminiscent of database transactions or application checkpoints in distributed
systems.

_LangGraph automatically checkpoints agent state so it can be recovered. Each run of the agent’s graph updates
the persistent state (identified by a thread_id). In this diagram, after the first interaction, the state (memory,
variables, etc.) is saved. On a new session (right), providing the same thread_id allows the agent to retrieve and
build on the previous state. This mechanism supports crash recovery, “time travel” debugging, and
seamless multi-turn interactions beyond a single process lifespan._

```
CrewAI (Storage of Long-Term Memory): CrewAI’s basic memory system stores long-term
memory in a local SQLite database (long_term_memory_storage.db), which naturally persists
across runs. While CrewAI doesn’t advertise stepwise checkpointing like LangGraph, it does
mean that any results an agent stores in long-term memory will be available if you re-instantiate the
agent later (as long as it points to the same storage file). CrewAI’s concept of “replay tasks from latest
crew kickoff” suggests you can rerun or recover prior runs, likely using the persisted outputs and the
Knowledge base. For short-term memory, CrewAI uses Chroma with a persistence directory –
meaning the vector DB of recent interactions is also saved to disk. If an agent crashes mid-execution,
the short-term memory on disk could be reloaded to continue context (though the framework’s
documentation emphasizes enabling memory makes it automatic, the developer might need to
ensure the same project name/path is used to reload). In essence, crash resilience in CrewAI comes
from the fact that it writes to files in the OS-specific application data folder by default , so
memory isn’t lost on process termination. A new run of the same “Crew” can pick up the existing
memory files. This is simpler but less granular than LangGraph’s checkpoint model.
```
```
AutoGen (Message History Lists): By default, AutoGen agents use in-memory message history
lists to maintain context. If an AutoGen agent stops, unless the history was saved externally, it’s
gone. However, AutoGen’s design encourages using external memory integrations for long-term
context. For example, the AutoGen + Zep integration demonstrates how an agent can push
conversation logs to the Zep service after each turn. Zep then stores and organizes these
into a knowledge graph of “facts,” which the agent can query later. To recover a session, an AutoGen
agent could reload relevant facts from Zep by conversation ID or user ID. Essentially, AutoGen
```
#### •

```
43
44 45
```
```
46
```
```
47
```
```
43
```
```
44 45
```
#### •

```
1
```
```
48 14
```
```
15
```
#### •

```
49
```
```
50
51 52
```

```
delegates persistence to external stores: you might plug in a vector DB or use Mem0 to save chat
history between runs. This gives flexibility – you can choose simple local storage in development
and a cloud DB in production – but it requires that you implement the glue (AutoGen doesn’t
automatically checkpoint everything for you).
```
```
Other strategies: Some agents use simpler checkpointing, like writing their internal plan or state to
a JSON file at intervals. For instance, an agent that maintains a task list could serialize that list to disk
every time a task is completed. On restart, it checks for that file and loads the last known task list to
resume work. If using a database, you might have a session_state table where the agent
periodically upserts a row with its latest state (e.g. current step number, partial solution, etc.). The
design of session recovery often involves capturing enough of the agent’s cognitive state (plans,
assumptions, memory content) so that a new agent instance can reconstruct the context.
LangGraph’s thorough approach does this automatically; in other setups, you might capture the
conversation plus some scratchpad of variables.
```
From a **Rust implementation perspective** , one could leverage these ideas by using transactions or
checkpoints in async workflows. For example, if building an agent loop in Rust, you might after each major
action do: sqlx::query!("INSERT OR REPLACE INTO state (session, step, data) VALUES
(?, ?, ?)", session_id, step_idx, state_json).execute(&db).await?;. By doing so, if the
process crashes at step _N_ , you can query the state table on restart to get step _N_ data and decide to
continue from there. For a lighter approach, using an append-only event log (like writing events to a file or
Kafka) can also enable recovery by replaying events up to the last one. The pattern should be tailored to the
agent’s workflow: linear script-like agents might just need the last state, whereas agents with branching
plans might need a full history or graph of states (which is where LangGraph’s solution is very handy).

## Distributed Memory Sharing

In a multi-agent system, especially one deployed as microservices or across machines, **shared memory** is
key to collaboration. When agents run in separate processes, they cannot rely on in-memory Python lists or
Rust HashMaps to share state – they need an external store that all can access. We’ve already highlighted
how using a networked store like Redis or a database enables this. Let’s delve a bit more into patterns and
concerns for distributed memory:

**Centralized Memory Store:** The simplest pattern is to choose a single persistence layer that all agents use.
This could be a cloud Postgres database, a Redis service, or a distributed vector store like Pinecone. All
agents are configured to use the same connection, and use identifiers (keys, session IDs, etc.) to isolate or
share data as appropriate. For example, if multiple agent instances are meant to work on the _same_ session,
they might all use a common session_id to tag their memory entries. A user proxy agent and a worker
agent could both read from memory:session123:context in Redis – one agent might write user inputs
there, the other reads and appends its responses. **LangGraph’s store namespace** concept illustrates this: it
uses a tuple like (user_id, "memories") as the namespace for memories. If all agents use the
same user_id namespace, they effectively share a memory space. The built-in RedisStore in LangGraph
is namespaced and even supports semantic search across all agents’ shared memory.

**Concurrency and Consistency:** With multiple agents writing, you must consider race conditions. Most
databases handle concurrent writes (they queue or lock appropriately). In Redis, operations are atomic per

```
53
```
#### •

```
54 55
```
```
6
```

command, but if you have a scenario like two agents trying to append to the same list, you might want to
use a single RPUSH command per agent input to avoid interleaving issues. Another tool is pub/sub or
streaming: e.g., Redis Streams or Kafka topics can allow agents to broadcast events and others to consume
in order. For instance, an agent could publish an event “AgentA finished subtask X” to a channel; other
agents subscribe and update their memory. This is more complex but useful in large multi-agent
ecosystems to maintain a **consistent view of state**.

**Memory Isolation:** Sometimes you want agents to share _some_ memory but keep other parts private. A
solution is to design keys or database tables such that certain data is scoped. For example, all agents might
share a global knowledge vector store, but each agent keeps its own scratchpad in separate keys (like
agent:{id}:working_memory). CrewAI touches on this with its **entity memory vs user memory** –
entity memory and short-term context are shared within a crew, but user-specific memory is separate

. In Rust, you could enforce this by giving each agent instance a different memory client or prefix.

**TTL/Expiry in Distributed Settings:** When multiple agents are reading/writing, using TTL to expire data
needs caution – one agent expiring a key might surprise another. If expiry is used (say to limit short-term
memory length), it’s wise to have agents coordinate or at least be aware (e.g. a convention that session data
expires after X time of inactivity). Alternatively, explicit deletion after a session ends might be better than
time-based expiry if multiple processes are involved.

**Example (Rust + Redis):** Suppose we have a manager agent and worker agents distributed. The manager
assigns tasks and each worker reports status. We could use Redis hash task:{task_id} to store task
details and update fields like status. All agents can read that. The manager could use a Redis PUBSUB
to notify when a new task is available. Workers subscribe, pick it up, then update the hash. This way,
memory (the set of tasks and their states) is shared and consistent. Another example is using a **shared
vector store** : if each agent is embedding new facts, they could all insert into a single vector index (with tags
to note the source agent or type of memory). A search query could then retrieve relevant facts regardless of
which agent learned them. The key is designing a schema or keyspace that logically merges the needed
memory.

CrewAI’s **“User Memory”** concept actually hints at multi-user, multi-agent scenarios: Mem0 (which CrewAI
used for user memory) can provide each user their own memory space, accessible to whichever agent
interacts with that user. If we imagine multiple agents (e.g. a scheduler agent and an email agent) both
serve the same user, they could both read the user’s memory from Mem0. In effect, Mem0 acts as a
centralized personal datastore for the user that any agent can query for preferences or history.

**Crash resilience in distributed context:** If one agent crashes, others might continue. The memory layer
should be robust so that a crashed agent’s last writes are not lost or partially applied. Using transactions (if
using SQL) or careful write strategies (append-only logs) can help. Also, agents can employ **heartbeats** or
leases on memory entries to detect a crashed peer and possibly take over tasks. These are advanced
patterns beyond the scope of memory storage basics, but they interact – e.g., storing a “lease owner” field
in a DB row for a task can coordinate which agent should handle it if one dies.

In summary, distributed agent memory relies on **centralization and naming conventions**. By picking a
central store (database, cache, or specialized service) and designing clear namespaces, you ensure all
agents literally “share the same memory.” The frameworks we’ve discussed (LangGraph, CrewAI) allow this:
LangGraph’s memory store can be backed by a database or Redis accessible to all nodes of an application

```
56
57
```
```
58
```
```
59
```

, and CrewAI’s external memory mode would allow plugging in a scalable cloud vector DB or
knowledge store. A Rust agent developer can follow suit by abstracting the memory interface and
using a remote client under the hood (for example, implementing a MemoryStore trait with a Redis-based
struct and a Postgres-based struct, and choosing which at runtime depending on deployment).

## TTL and Memory Expiry Strategies

In continuous operations, agents can accumulate vast amounts of memory. To prevent unbounded growth
and to respect privacy or relevance concerns, it’s important to implement **TTL (time-to-live)** or other expiry
strategies for memory:

```
Built-in TTL in caches: As noted, systems like Redis make it easy to set an expiry on keys. If an
agent’s short-term memory is stored in Redis, one could set a TTL of say 1 hour on each
conversation key. That means an hour after the last update, the memory disappears (garbage-
collected by Redis). This is useful for ephemeral chat context that shouldn’t persist forever. In a Rust
implementation using redis-rs, you can call expire("session:123", 3600).await after
setting the data. The advantage is simplicity and ensuring stale data clears out. The downside is
sudden disappearance – an agent returning to a conversation after the TTL passes may find no
memory unless you handle that (maybe by storing a summary elsewhere).
```
```
Application-managed expiry: For stores that don’t have native TTL (e.g. vector DBs, SQL DBs), you
can include timestamps in data and enforce expiry in query logic. For instance, add a created_at
field to each memory record and modify your retrieval queries to ignore records older than X (or
explicitly delete them in a background job). LangGraph’s memory items include timestamps
(created_at, updated_at) , which could facilitate such filtering. If you search the store, you
could filter out items by time or only take the last N items. Mem0’s approach (from what it
suggests) likely involves summarizing older interactions so the memory store contains mostly
recent, salient info.
```
```
Sliding window and summarization: A common strategy for chatbots is to keep a sliding window of
the last N interactions in short-term memory, and either drop or summarize older content.
Summarization compresses old memory into a shorter form (which could itself be stored as a
memory entry). This isn’t TTL per se, but it’s a form of expiry by compression – irrelevant details are
gone, only important points remain. An autonomous agent could periodically review its log and
create a summary note (stored maybe in a vector DB with a tag “summary”) and then prune the
detailed records. The Generative Agents paper (Park et al. 2023) did this by having an “aging”
mechanism where less-accessed memories get summarized to free capacity for new ones.
```
```
Temporal reasoning systems: More advanced frameworks like Graphiti incorporate temporal expiry
explicitly. Each fact has an invalid_at or expired_at timestamp if it’s no longer true. This
doesn’t remove the fact from the store (they keep history), but the query engine can exclude those
that are expired unless explicitly asked for historical info. This approach is great for maintaining a
full history while making sure the agent’s active memory only considers currently valid info. If
implementing your own, you might mark records with an “active” flag or an end-date.
```
```
5 6
60 61
```
#### •

#### •

```
62
63
```
```
64
```
#### •

#### •

```
28
```

```
TTL for vector stores: Many vector DBs don’t directly support TTL, but you can simulate it. One
could periodically run a job to delete vectors older than X days or beyond a certain count. Another
tactic is to use metadata filtering during retrieval – e.g. add a metadata field session_date and
when querying for context, filter to only include the last week. This way, even if the data is still
stored, it won’t interfere with retrieval after its effective expiry.
```
When using TTL/expiry, consider **trade-off between forgetting and utility**. If an agent forgets too
aggressively, it might lose valuable context. On the other hand, keeping everything can slow down retrieval
and use unnecessary memory. A sensible approach is multi-tier: keep recent memory raw, keep mid-term
memory summarized, and drop or archive very old memory. For instance, a coding agent might keep the
current project’s chat logs fully, summarize older project discussions into a wiki (perhaps vector-indexed),
and drop anything beyond a few projects old.

From the frameworks: CrewAI’s docs do not explicitly mention TTL, but since short-term memory is tied to
each execution, one could imagine it resets short-term memory when a new “crew” starts (unless the same
memory files are reloaded). LangGraph’s approach could allow TTL by simply not carrying thread state
forward or by cleaning the store for old threads. Mem0’s literature emphasizes it “continuously learns” and
retrieves salient info , likely implying some pruning of non-salient info (perhaps by measuring “salience”
and dropping low value items over time ).

**Implementing TTL in Rust Example:** If using tokio and Redis, one can spawn a periodic task that scans
keys with a certain prefix and calls expire or deletes them based on timestamp metadata. Alternatively, if
using a SQL DB, one could add WHERE timestamp > now() - interval '7 days' in queries.
Libraries like chrono in Rust can help compute cutoff times. Always ensure that the system time is reliable
if using time-based expiry (especially in distributed settings, consider clock sync).

## Framework Examples and Memory Designs

Let’s look more concretely at how specific frameworks handle memory, tying together the above concepts:

### LangGraph (by LangChain)

LangGraph provides a **graph-based LLM workflow** with first-class persistence support. Its memory
architecture is **flexible and developer-controlled** :

```
Short-term vs Long-term: LangGraph distinguishes between transient state during an agent’s run
and persistent memory across runs. Short-term memory can simply be part of the agent’s state
(e.g. a variable holding recent messages) that gets checkpointed. Long-term memory typically
involves an external store, like a vector database, where you can save information to use in later
sessions. However, LangGraph does not force a particular long-term store – it provides
interfaces to integrate them.
```
```
Threads and Checkpoints: As discussed, each session can be given a thread_id. The
checkpointer will save the entire state graph after each step to a thread (which you can think of as a
session log in the LangGraph backend). This means you can recover the state or even fork
new sessions from a past state. It’s very powerful for fault tolerance and for iterative development
```
#### •

```
64
65
```
```
66 67
```
#### •

```
68
```
```
69 67
```
#### •

```
44 45
```

```
(you can “time-travel” to a previous state and try a different strategy). This design is somewhat
unique to LangGraph among agent frameworks, reflecting its goal for durable execution.
```
```
Memory Store (Cross-thread memory): LangGraph introduces a Store interface to retain
information across threads. If checkpointing is for per-session state, the Store is for global or
user-specific knowledge that transcends one conversation. The store can be an in-memory
Python dict (good for testing), or something like a RedisStore or a custom database-backed store.
Data in the store is organized by namespace (which can be tuple identifiers like (user_id,
"memories")) and each entry is an Item with a value and metadata. This allows multiple
separate memory categories (you could have different namespaces for different knowledge types, or
one per user). LangGraph’s store natively supports semantic search over the stored items if an
embedding model is configured. This is essentially built-in RAG: you can ask the store “find
memories about topic X” and it will return similar items.
```
```
Usage in nodes: Within a LangGraph agent’s code (nodes of the graph), the framework makes it
easy to access the store and the config. For example, you can define a node function that takes
store: BaseStore and config: RunnableConfig as parameters, and LangGraph will inject
those when running. This lets you write logic like: retrieve the user_id from config, then do
store.search((user_id, "memories"), query="...") to get relevant memory. You can
also store.put(...) new information as the agent learns it. In this way, the agent’s code
directly interacts with persistent memory in a controlled fashion.
```
```
Fault tolerance and multi-agent: LangGraph’s capabilities list explicitly mentions Time Travel and
Fault-tolerance as benefits of persistence. It also has a concept of Multi-agent support
(there’s a section in docs) – presumably you can have multiple subgraphs or agents interacting, and
their shared state can be persisted and coordinated. While not trivial, you could model each agent as
part of the state graph (or separate threads that share a store).
```
Overall, LangGraph is **developer-friendly but requires understanding of its abstractions**. The advantage
is you can practically get the best of all worlds: a reliable state machine via checkpoints, plus a semantic
memory store, plus any custom logic you need. The downside might be complexity – writing your workflow
as a StateGraph with correct schemas takes effort. But for someone building a serious autonomous
agent in Rust, one might take inspiration from LangGraph: implementing a trait to save state every step
(like LangGraph’s checkpointer interface) and possibly having a generic Store trait for cross-run memory.
The key takeaway is how LangGraph cleanly separates _transient state persistence_ (for continuity and crash
recovery) from _long-term memory storage_ (for knowledge retention).

### CrewAI (Crew Architectures)

CrewAI focuses on multi-agent “crew” orchestration with roles, and it comes with a **structured memory
system out-of-the-box** :

```
Built-in Memory Types: CrewAI defines five memory types : Short-Term, Long-Term, Entity,
Contextual, and User memory. In the basic usage, enabling memory=True on a Crew will
activate short-term, long-term, and entity memory automatically. Short-term uses a RAG
vector store (Chroma) to capture recent context (relevant info from the current run), and is typically
reset or isolated per execution. Long-term uses SQLite to persist important outcomes so they remain
```
```
43
```
#### •

```
70
71 72
```
```
73 62
```
```
16
```
#### •

```
74
75
76
```
#### •

```
77 78
```
```
79 80
```
```
81
```
#### •

```
82
83 1
```

```
for future runs. Entity memory also uses RAG (vector embeddings) but specifically to track
information about key entities (which could be people, places, or concepts) across interactions.
Contextual memory is essentially an aggregator combining short, long, and entity memory to
provide a coherent context to agents. User memory (a legacy approach in CrewAI) integrates
with Mem0 to give each user their own memory store (likely a cloud-based personalized memory)
```
- this one is optional and being superseded by more flexible external memory, but it’s
conceptually a long-term memory keyed by user.

```
Activation and Storage: Using CrewAI’s memory is straightforward: you literally just turn it on in the
Crew config (and optionally specify an embedding provider/model). Under the hood, CrewAI
decides where to store things. By default, it uses the appdirs library to pick a filesystem location
in the user’s home directory for storing memory files. We saw an example path for macOS: it
stores Chroma DB files for knowledge, short_term_memory, long_term_memory, entities, and a
SQLite file for long_term_storage. This means if you run a CrewAI project named "MyProject", it
will persist those memory files on disk; if you run it again, it will load existing memory (unless you
wipe those files). This default makes development easy (no extra infrastructure) and gives basic
persistence across sessions.
```
```
External Memory Providers: Recognizing that the built-in approach might not scale or fit all needs,
CrewAI introduced an External Memory interface. This allows plugging in custom memory
backends, effectively decoupling memory storage from the Crew execution. The documentation
specifically mentions Mem0 as an external provider example (and indeed provides a Mem
integration code snippet). External memory mode is ideal if you want to use, say, a cloud vector
database or a different vector index library. The framework likely provides a base class to implement
search and add operations so that CrewAI agents can call memory without knowing if it’s local or
external.
```
```
Memory Isolation and Sharing: By design, memory in CrewAI is per crew (which is essentially the
group of agents working on a set of tasks). Within a crew, agents presumably share the short-term
and entity memory (since it’s said to help them collaborate within a single crew execution ). If you
have separate crew instances (like separate projects or separate user sessions), their memory is
isolated to different file folders or different collections. There was a user question about multi-
agents with memory isolation – likely if one wanted sub-teams of agents each with their own
memory while still coordinating, which can be complex. But for most use, one Crew’s memory is not
seen by another unless explicitly merged.
```
```
Session recovery: CrewAI doesn’t explicitly advertise checkpointing per step. However, since long-
term memory logs results, an agent could read those past results on a new run to “recover”
knowledge. Also, because short-term memory is persisted to a file, if a crew stops and restarts (with
the same memory path), the new agents could query the ChromaDB to get relevant past info
(though one might consider those “past info” now as long-term knowledge in context). CrewAI also
offers a feature to replay the last execution which suggests it can rerun tasks – possibly
reading from memory or from an event log.
```
```
TTL in CrewAI: There’s no automatic TTL mentioned, but short-term memory is conceptually per
run. You might manually clear or limit it if needed (there is mention of allow_reset=False in a
config snippet for Chroma – suggesting you can prevent memory reset between runs if you want
```
```
1
56
```
```
84
```
```
59
```
#### •

```
85 86
```
```
87 88
```
```
88
```
#### •

```
60
```
```
89
```
#### •

```
90
```
#### •

```
91
```
#### •


```
continuous accumulation ). The long-term SQLite could grow; a developer might prune old
entries if needed (maybe via an API or manually connecting to the DB).
```
In summary, CrewAI provides a **structured, somewhat opinionated memory system**. It’s great for quickly
giving agents memory (no need to set up Pinecone or design schemas – just toggle it on). It covers different
memory scopes (recent vs. long-term vs. entity-specific). The trade-off is you are limited by their choices
(SQLite for long-term – which as noted might be a bottleneck at scale – and Chroma for vectors).
However, with external memory plugins, you can overcome that by plugging in your own storage solution
while still using CrewAI’s abstractions. For someone implementing in Rust, CrewAI’s design is a hint that a
combination of approaches is useful: perhaps use an **embedded DB** for logging key results (it’s easy and
reliable), and a **vector index** for semantic recall, plus maybe a separate structure for entity-centric info.
Also, having a simple switch to enable/disable memory is user-friendly – if memory=False, the agent runs
stateless (which can be good for testing or certain use cases), but memory=True gives it full capabilities.

### AutoGen (Microsoft)

AutoGen (not to be confused with AutoGPT) is a framework focusing on multi-agent conversations and tool
use. Its approach to memory is a bit different: it emphasizes conversation history and lets developers
integrate external memory as needed.

```
Message History as Short-Term Memory: AutoGen agents (like AssistantAgent and
UserProxyAgent) maintain an internal list of messages exchanged – essentially the chat transcript
```
. This is used as the conversation context window when generating new responses. In many
examples, you see code like agent.run(task="...") which internally will append the prompt to
the message list, call the LLM, get a reply, append it, etc. This message list is ephemeral by default –
it lives in memory while the program runs. This suffices for short dialogues, but if you drop the agent
instance and recreate it later, it won’t recall previous chats unless you saved them.

```
External Long-Term Memory: For anything beyond the immediate conversation, AutoGen relies on
external integrations. The philosophy is to let developers choose the storage that fits their
needs. Microsoft provides some integrations: e.g. the AutoGen docs show how to use Zep , which is a
cloud memory service building a knowledge graph of chats. In the Zep integration, they
created a ZepConversableAgent subclass that on each message send, also sends the message to
Zep (which stores it and extracts facts). When the agent needs to recall, it queries Zep for
relevant facts from history. Similarly, AutoGen has tutorials for using RetrieveChat with PGVector,
MongoDB, Couchbase, Qdrant etc. (those links in [14] show a variety of RAG sources). This indicates
AutoGen itself doesn’t come with one single memory backend but provides examples to hook into
many popular data stores.
```
```
Mem0 and others: AutoGen is closely affiliated with some Microsoft research; Mem0 (Memory Zero)
was introduced as a long-term memory architecture for agents (there’s even a Microsoft guide for
Mem0 ). It’s possible AutoGen can integrate Mem0 as well. Mem0 is an advanced memory system
that dynamically captures key facts and uses vector recall plus update strategies. If a developer
wanted, they could combine Mem0 with AutoGen – perhaps by writing a MemoryProvider that calls
Mem0’s API or library to store/retrieve interactions. This is speculative, but given Mem0’s open-
source nature and aim to be a generic memory layer , it aligns with AutoGen’s approach of
plugging in external solutions.
```
```
92
```
```
4
```
```
93 49
```
#### •

```
94
```
#### •

```
50
```
```
51 52
```
```
95 96
```
#### •

```
97
65
```
```
98
```

```
No default persistent memory: Unlike LangGraph or CrewAI, an AutoGen agent won’t remember
anything from a past run unless you explicitly wire it up. This means session recovery is manual:
you’d have to save the agent.messages list somewhere and later restore it into a new agent. Or
use an external DB as mentioned. The advantage of this design is simplicity and minimal
dependencies. The drawback is the onus is on the developer to not lose important context. The
dev.to comparative analysis summarized that “AutoGen relies on message lists and external
integrations” and noted that purely using the message list may not suffice for complex
reasoning that requires deeper memory.
```
```
Distributed scenarios: AutoGen has a concept called AgentTeams (or agent chat groups) for multi-
agent dialogues. If you have multiple agents in AutoGen interacting (a group chat), each agent has
its own message history, but they also share a conversation in the sense messages are passed
around. For memory, you might then also share an external store. AutoGen doesn’t inherently sync
memories between agents except through conversation – if they need a shared knowledge base,
you’d integrate a shared vector store or DB that both can query. For example, if you had a coding
agent and a testing agent, you might use a common Chroma DB where the coding agent stores
implemented functions and the testing agent can retrieve them (this isn’t provided out-of-box, but
the framework wouldn’t prevent you from doing it).
```
```
Example integration (AutoGen + Chroma): The AutoGen docs show creating a
ChromaDBVectorMemory and assigning it to an agent’s memory list. In that snippet
, they configure a persistent Chroma collection and then give the assistant agent
memory=[rag_memory]. The agent, during operation, can use self.memory to add or query
items. So AutoGen’s agents are built to accept memory modules; it’s just that the framework doesn’t
mandate which one. This design is nice if you want to experiment with different memory solutions
(vector vs. SQL vs. custom) under a unified interface.
```
**Takeaway for Rust devs:** If following AutoGen’s model, one would create an **abstraction for memory** and
allow plugging various backend implementations. For instance, define a trait Memory with
add(content), query(query) -> contents methods. Then implement ChromaMemory,
PostgresMemory, GraphMemory that satisfy it. The agent can hold a list of Box<dyn Memory> and
use them as needed. AutoGen’s open-source code likely has something similar (perhaps using Python async
and polymorphism). This is a flexible but developer-driven approach.

AutoGen’s approach underscores the difference between **conversation history** and **knowledge memory**.
The former (message list) is used directly in prompts (“Here’s the last 10 messages...”), whereas the latter
(external memory) is used to fetch supplemental info (“Recall: the user’s preference from last week was X”).
Both are important. In our own agent designs, we should ensure to maintain a **dialogue context** (which
might be truncated for token limits, but conceptually it’s the short-term memory), as well as a **long-term
knowledge base** that can be queried when needed. RAG is typically for the latter.

#### •

```
93 49
99
```
#### •

#### •

```
100 101 102
103
```

### Other Noteworthy Approaches

Beyond the three frameworks above, it’s worth mentioning a few additional patterns and projects,
especially relevant to **autonomous coding agents and specialized domains** :

```
Mem0 (Memory Layer): Mem0 is a project (by a team including Microsoft researchers) aiming to
provide a unified memory service for AI agents. It frames memory in types: semantic memory,
episodic memory, etc. and claims to dynamically extract and retrieve key conversational facts
```
. Essentially, Mem0 can act as a drop-in long-term memory: agents send their interactions to
Mem0, which uses algorithms (likely vector embeddings, summarization, etc.) to store them
efficiently and return relevant context later. It’s open source and integrates with standards like
the **MCP (Memory Communication Protocol)** for agents. CrewAI’s user memory was a direct
integration of Mem0 (legacy). Mem0’s architecture (per their arXiv paper ) emphasizes
scalability and improved accuracy by focusing on “salient information”. This kind of service could be
extremely useful for developers who don’t want to build memory from scratch – you could have your
Rust agent call Mem0’s API (maybe via HTTP/JSON or a gRPC) to save and load memories, offloading
the heavy lifting of vector search and summarization to that service.

```
AutoGPT and BabyAGI successors: These popular agent examples used fairly simple memory
initially (text files or local vectors), but they spurred development of better patterns. Newer iterations
(e.g. AutoGPT Beta, AgentGPT, etc.) have introduced centralized memory management where all
data is routed through one manager that could be backed by various stores. One interesting
idea from the community has been using a database plus an LLM to query it. For example, instead
of vector search for everything, store all facts in a PostgreSQL and have the agent use a tool that
translates natural language queries into SQL (taking advantage of the structured info). This
approach was even advocated on a forum: “skip the RAG circus... use a robust SQL DB and a Text-to-SQL
agent” for simplicity and precision. This is viable if you can structure a lot of your agent’s
knowledge, but less so for raw text or big documents. It highlights that sometimes good old
databases with a pinch of NLP for querying can solve memory retrieval, especially for business data
or code that can be indexed.
```
```
Generative Agents (Long Horizon Simulation): In the research paper by Park et al., they created AI
agents with long-term memory in a Sims-like environment. Their approach was to record every
observation and action as a memory entry, each with an importance score and a timestamp. Over
time, they forgot less important details and synthesized higher-level reflections from time to time.
For example, after a day of events, an agent might store a summary like “I went to the café and met
John who talked about his new job.” This is a form of compression. Retrieval was done by combining
recency, importance, and embedding similarity to the query. While not a production system, this
approach is instructive: it shows that memory management can involve cognitive operations
(scoring, summarizing) , not just database CRUD. Downstream agents might adopt similar patterns:
e.g., a coding agent could assign higher “importance” to tests that failed or critical design decisions,
so those memories persist, whereas trivial log messages fade out. Implementing this might involve a
background task that periodically prunes memory (maybe using a priority queue keyed by
importance) and generates summary records for older stuff.
```
```
Voyager (Code as Memory): The Voyager agent in Minecraft demonstrated a novel memory: an
ever-growing skill library. It treated code as memory – each new skill learned (in the form of a
```
#### •

```
98
104
105
```
```
106
107
59 108
```
#### •

```
10
```
```
40
```
#### •

#### •

```
37
```

```
Python function) was added to its library (written to disk), and it could reuse those skills later as
tools. This approach is very relevant for software development agents: the code they write is a form
of long-term memory. Future agents can search the codebase (using either semantic search or static
analysis) to figure out how a previous problem was solved. For instance, if an agent solved a file
parsing task yesterday by writing a function, it can retrieve that function for a similar task today
instead of writing from scratch. Storing code in a git repository or file system thus serves as durable
memory. One can enhance this by indexing the code (with a vector store or even just grep) for easy
retrieval. In Rust, one could let the agent call rg (ripgrep) or a built-in code search tool to find
relevant snippets from its project directory. This is more a functional memory than a conversational
one, but it’s critical for agents that iteratively build software.
```
```
TTL and forgetting in practice: Some commercial systems allow user-configurable memory
retention. For example, an enterprise chatbot might have a setting to “forget conversation after 24
hours” for compliance. Implementation-wise, this could simply mean each conversation session ID is
tied to a TTL in the DB or the memory entries are timestamped and the agent or system refuses to
access ones beyond cutoff. A robust agent should be able to handle memory not found (and either
ask the user to recap or start fresh). If required to implement such policy, a developer can leverage
the techniques we covered (e.g. TTL in Redis, or scheduled deletes in SQL).
```
Finally, a quick note on **migration patterns** : as your agent evolves, you might start with a simple memory
and then need to migrate to a more scalable one. Perhaps you prototype with an embedded ChromaDB
and later move to Pinecone for production. Or move from a local JSON file to SQLite, then to Postgres. To
make this easier, design your memory interface with an eye on **data export/import**. For example, you
could have a routine to dump all memories from SQLite to a CSV or JSON, which you can then re-ingest into
a new store. Or if using vector DB, ensure you have the original texts stored so you can re-embed with a
new model if needed. LangGraph’s checkpointer libraries hint at this – they mention a **Serializer** interface
with optional encryption , meaning you can control how state is saved (maybe to allow version
upgrades). In Rust, if using Serde for state, you can version your structs for future compatibility (so older
checkpoint files can still be read after code changes).

## Code Patterns and Storage Schemas

To make things concrete, let’s outline a few code and schema snippets for different persistence layers in a
Rust (Tokio async) context. These patterns show how one might integrate the discussed storage types:

**1. Defining a Memory Trait:** We create a trait to abstract memory operations, so we can plug in different
backends:

```
#[async_trait::async_trait]
pubtrait MemoryStore {
async fnput(&self, key:&str, value: &str) ->anyhow::Result<()>;
async fnget(&self, key:&str) ->anyhow::Result<Option<String>>;
async fnsearch(&self, query:&str) ->anyhow::Result<Vec<String>>;
// search could be a no-op for stores that don't support semantic search
}
```
#### •

```
109
```

We might implement this for several backends:

```
In-memory (for testing):
```
```
usetokio::sync::RwLock;
usestd::collections::HashMap;
```
```
pubstruct InMemoryStore {
data: RwLock<HashMap<String, String>>,
}
```
```
#[async_trait::async_trait]
implMemoryStore forInMemoryStore {
async fnput(&self, key:&str, value: &str) ->anyhow::Result<()> {
self.data.write().await.insert(key.to_string(),value.to_string());
Ok(())
}
async fnget(&self, key:&str) ->anyhow::Result<Option<String>> {
Ok(self.data.read().await.get(key).cloned())
}
async fnsearch(&self, _query: &str) ->anyhow::Result<Vec<String>> {
// No semantic search, just return all values (or empty)
Ok(Vec::new())
}
}
```
This simple store can hold key-value pairs within a single process. It obviously doesn’t persist after the
program ends, but is fine for ephemeral memory during a run or unit tests.

```
SQLite (Relational) Implementation: We might have a table memory(key TEXT PRIMARY KEY,
value TEXT, timestamp DATETIME). Using sqlx:
```
```
pubstruct SQLiteMemory{
pool: sqlx::SqlitePool,
}
```
```
implSQLiteMemory{
pub asyncfn new(path: &str) ->anyhow::Result<Self> {
let pool= sqlx::SqlitePool::connect(path).await?;
// Ensure table exists
sqlx::query("CREATE TABLE IF NOT EXISTS memory (key TEXT PRIMARY KEY,
value TEXT, timestamp DATETIME)").execute(&pool).await?;
Ok(Self{ pool })
}
}
```
#### •

#### •


```
#[async_trait::async_trait]
implMemoryStore forSQLiteMemory{
async fnput(&self, key:&str, value: &str) ->anyhow::Result<()> {
sqlx::query("INSERT OR REPLACE INTO memory (key, value, timestamp)
VALUES (?1, ?2, datetime('now'))")
.bind(key).bind(value).execute(&self.pool).await?;
Ok(())
}
async fnget(&self, key:&str) ->anyhow::Result<Option<String>> {
let rec= sqlx::query!("SELECT value FROM memory WHERE key = ?1", key)
.fetch_optional(&self.pool).await?;
Ok(rec.map(|r| r.value))
}
async fnsearch(&self, query:&str) ->anyhow::Result<Vec<String>> {
// A simple LIKE query for demonstration (full-text search would be
better for large text)
let pattern= format!("%{}%", query);
let rows= sqlx::query!("SELECT value FROM memory WHERE value LIKE ?1
LIMIT 10", pattern)
.fetch_all(&self.pool).await?;
Ok(rows.into_iter().map(|r| r.value).collect())
}
}
```
Here, put upserts a record with the current timestamp. get retrieves by exact key. search does a
naive SQL LIKE search; for more sophistication, one might use SQLite’s FTS (Full Text Search) extension or
integrate PG if needed. This provides durable storage and allows partial text querying (though not
semantic). A schema like this is suitable for storing small text items (e.g., summary of interaction or a JSON
of agent state). If we wanted to store vectors in SQLite, we could store them as blob or use a separate table,
but usually we’d use a specialized vector store instead of SQL for that.

```
Redis (Key–Value with TTL):
```
```
pubstruct RedisMemory {
client: redis::Client,
}
```
```
implRedisMemory {
pub fnnew(url: &str) ->anyhow::Result<Self> {
let client= redis::Client::open(url)?;
Ok(Self{ client })
}
fn conn(&self) ->redis::RedisResult<redis::Connection> {
self.client.get_connection()
}
}
```
#### •


```
#[async_trait::async_trait]
implMemoryStore forRedisMemory {
async fnput(&self, key:&str, value: &str) ->anyhow::Result<()> {
// Using a blocking redis call for simplicity; use async in real code
with Tokio
let mutcon = self.conn()?;
redis::cmd("SET").arg(key).arg(value).query(&mut con)?;
// Optionally set an expiry, e.g., 1 hour
redis::cmd("EXPIRE").arg(key).arg(3600).query(&mutcon)?;
Ok(())
}
async fnget(&self, key:&str) ->anyhow::Result<Option<String>> {
let mutcon = self.conn()?;
let result: Option<String> = redis::cmd("GET").arg(key).query(&mut
con)?;
Ok(result)
}
async fnsearch(&self, _query: &str) ->anyhow::Result<Vec<String>> {
// For Redis, we'd need to do a KEYS search or use a secondary index.
Ok(vec![])
}
}
```
This shows basic usage: SET a key and use EXPIRE to add a TTL. In practice, one might use
redis::aio::Connection for async or deadpool-redis for a pool. Searching by content is not what
Redis is made for (unless you use RedisSearch module or maintain your own mapping from keywords to
keys). Typically, you’d know the keys to get in a Redis scenario (like storing by session or user). In an agent
context, you might use a composite key scheme: e.g., memory:{user}:{category}. So search might be
implemented by doing KEYS memory:123:* to list all keys for user 123 and then GET each – not
scalable for large sets, but fine if limited.

```
Vector Store (using an external service): For demonstration, suppose we use Qdrant (an open-
source vector DB with a REST API and a Rust SDK). We won’t write full code, but conceptually:
```
```
pubstruct QdrantMemory{
client: qdrant_client::QdrantClient,
collection: String,
}
```
```
#[async_trait::async_trait]
implMemoryStore forQdrantMemory{
async fnput(&self, key:&str, value: &str) ->anyhow::Result<()> {
// We would embed the value text to a vector (maybe call an embedding
service).
let embedding= embed_text(value).await?;
// Store in Qdrant with the key as an external ID or as part of payload.
```
#### •


```
let payload= json!({ "key": key, "text": value});
self.client.upsert_point(&self.collection, None, &payload,
&embedding).await?;
Ok(())
}
async fnget(&self, key:&str) ->anyhow::Result<Option<String>> {
// We can use key filtering to find the point
iflet Some(point) = self.client.scroll(&self.collection).filter("key",
key).first().await? {
// Assuming the payload stored the text
ifletSome(text) = point.payload.get("text").and_then(|v|
v.as_str()) {
return Ok(Some(text.to_string()));
}
}
Ok(None)
}
async fnsearch(&self, query:&str) ->anyhow::Result<Vec<String>> {
let embedding= embed_text(query).await?;
let results= self.client.search_points(&self.collection, &embedding,
5, None).await?;
let muttexts = Vec::new();
for resinresults {
ifletSome(p) = res.payload.get("text").and_then(|v| v.as_str()) {
texts.push(p.to_string());
}
}
Ok(texts)
}
}
```
Here embed_text is a placeholder for an embedding function (which could call OpenAI’s API or use a local
model). We use Qdrant’s client to upsert a point with vector and payload, and search by vector. This shows
how a vector store might be wrapped to fit our MemoryStore interface. In practice, one might not
implement get by key for a vector store (since vector DBs are more about similarity search than direct key
lookup, but we included it for completeness by storing the key in payload).

Also, note that vector search is approximate by default; however, many vector DBs allow filtering by
metadata. For instance, Qdrant could filter by user_id or memory_type fields. This way, you can
implement things like “search only in memories of type X or for user Y” – analogous to namespacing. This is
important to restrict the semantic search to relevant subsets when needed (like separate knowledge
categories).

```
Graph DB: A Rust integration might use the Neo4j Bolt driver or an HTTP API for something like
Memgraph. One could model memory with nodes for agents, tools, concepts and edges for
relationships. For brevity, we won’t write full code, but an example Cypher query might be:
```
#### •


```
To add a fact: MERGE (u:User {id:$uid}) MERGE (p:Preference {name:$pref}) MERGE
(u)-[r:LIKES {since:date()}]->(p) – this creates a relationship that user likes a preference
with a timestamp.
```
```
To update a fact (if user changes preference): either add a new relationship (and perhaps archive the
old one by adding an expired property).
To query: MATCH (u:User {id:$uid})-[r:LIKES]->(p:Preference) WHERE r.active =
true RETURN p.name to get current preferences.
```
In Rust, using the neo4j-client crate, one could send these Cypher queries. The memory trait could be more
complex here (graph queries don’t fit a simple put/get interface). Possibly we’d have specialized functions
like fn add_fact(subject, relation, object) and maybe not implement the same MemoryStore
trait. Graphs might require a different abstraction.

**Storage Schema Design:**

```
For Relational DB schemas , you might use multiple tables depending on memory types. E.g. a
conversations table (session_id, role, content, timestamp) to store dialogue history; a
knowledge table (id, content, source, vector) if using PGVector or storing references to vector
store IDs; a agent_state table (agent_id, key, value) for any other state variables. If designing for
an agent that plans and executes, you might have a tasks table (task_id, description, status,
result, timestamp). The key is to identify what entities exist in your agent’s world (messages, tasks,
facts, etc.) and give them structured storage. This can overlap with an ontology if you have one.
```
```
For Vector store schemas , typically you choose a collection name (like “AgentMemory”) and decide
on the payload fields. Common practice is to store at least: the original text (content), maybe a
type (e.g. “conversation” vs “documentation”), possibly a session_id or user_id if you want
to scope queries, and any other metadata (source, timestamps). Most vector DBs allow filterable
metadata, so leverage that to keep things organized. E.g., you could ensure that when an agent in
session X searches memory, you filter session_id = X OR session_id IS NULL (to get global
knowledge plus session-specific memory).
```
```
For key–value schema , it’s about key naming conventions. A hierarchical naming (like
project:XYZ:agent:ABC:memory) can encode scope. If using Redis hashes, you could do HSET
agent:123 memory "..." to keep a blob of memory. Or lists: LPUSH conversation:123
"<agent>: <message>" to build a transcript list, which the agent can fetch with LRANGE
conversation:123 0 -1 to get all messages.
```
```
For graph schemas , define node labels for each entity type your agent handles and relation types
for the relationships of interest. For a coding agent: nodes might be Function, File,
Requirement, Bug , with relations like DEPENDS_ON (Function -> Function), DEFINED_IN
(Function -> File), SATISFIES (Function -> Requirement), SIMILAR_TO (between Functions
perhaps via an embedding vector comparison stored as a property). For a conversational agent:
nodes User, Agent, Topic, etc., relations MENTIONS (Agent -> Topic), KNOWS (User -> Fact).
The graph can become as rich as needed. Ensure to include time if needed, either as properties or as
separate temporal nodes.
```
#### • • • • • • •


**Concrete Trade-offs Recap:** Let’s finalize with a quick summary tailored to building autonomous agents:

```
Relational vs Vector: Use relational for exact, critical data (like tracking tasks, counters, configs)
where accuracy matters and data is structured. Use vector for semantic context injection (like
recalling a helpful log snippet or knowledge doc) where fuzzy matching is needed. They can
complement – e.g., store documents in a SQL table and also store their embeddings in a vector
index, using SQL for metadata filtering and vector DB for similarity.
```
```
Key-Value vs Graph: Use key-value (or simple caches) for fast ephemeral sharing – e.g. synchronizing
two agents in real time about a single value (like the latest user query or current step number). Use a
graph if the agent’s world model is complex enough to warrant multi-hop reasoning – graphs shine
when an agent needs to systematically traverse connections (like finding a chain of events or
relationships). The Graphiti example demonstrates that for dynamic, real-time data, a graph can
maintain consistency where a pure vector approach might not.
```
```
Embedded vs External: In early development or low-scale deployments, embedded (local) stores are
usually sufficient and simpler. But plan for an upgrade path : e.g., design your code so switching
from a local Chroma to a cloud Weaviate, or from local file logs to a cloud database, is not a
complete rewrite. Abstracting memory logic (as we did with a trait) helps. Also, test crash recovery
early: kill your agent process and restart to see if it picks up where left off. Using something like
SQLite or persistent Chroma locally will simulate this.
```
```
TTL/expiry: Implement policies appropriate for your use case. If building a personal assistant that
shouldn’t remember things indefinitely for privacy, ensure to wipe or encrypt old memory (some
frameworks even support encryption – LangGraph mentions a serializer with encryption option ).
If building a long-term autonomous system (like an automated dev team that works 24/7), decide
how it will archive older knowledge – perhaps move old project data to an archive DB once a project
is finished, so active memory stays relevant.
```
```
Code as memory: For software agents, leverage the environment. The file system (code files, output
artifacts) is a form of memory that persists. Many agent frameworks, including those above, allow
tool use – e.g., an agent can be given a filesystem tool. By writing important information to files (and
reading later), an agent creates an implicit memory. Downstream, consider coupling such file writes
with more formal memory: for example, after an agent writes design.md with a summary of its
plan, also index that summary in the vector store for easy retrieval. Redundancy between file
outputs and memory store can provide both human-readable records and machine-friendly recall.
```
In conclusion, building a memory layer for autonomous agents involves blending **multiple storage
paradigms**. Mainstream methods like vector databases empower semantic recall, while classic databases
ensure reliable state tracking – and newer ideas like knowledge graphs offer structured, temporal
awareness. The best solution often mixes these: e.g. CrewAI’s combination of RAG and SQL , or
LangGraph’s threads plus store design. By understanding the strengths of each approach and
learning from existing frameworks, developers can architect a memory subsystem that allows their Rust-
based agents to remember, recover, and reason about past experiences in a robust way. With careful design
(and citing of sources! ), your autonomous agents will not only be smart but also **persistent** in pursuit of
their goals.